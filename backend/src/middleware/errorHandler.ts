import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';

import { logger } from '../config/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = StatusCodes.INTERNAL_SERVER_ERROR
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  error: AppError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  const { method, url, ip } = req;
  const statusCode = error.statusCode || StatusCodes.INTERNAL_SERVER_ERROR;
  const message = error.message || 'Internal Server Error';

  // Log error details
  logger.error('Error occurred:', {
    error: {
      message: error.message,
      stack: error.stack,
      statusCode,
    },
    request: {
      method,
      url,
      ip,
      userAgent: req.get('User-Agent'),
    },
  });

  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  const errorResponse = {
    success: false,
    error: {
      message: isDevelopment ? message : getGenericErrorMessage(statusCode),
      ...(isDevelopment && { stack: error.stack }),
    },
    timestamp: new Date().toISOString(),
    path: url,
  };

  res.status(statusCode).json(errorResponse);
};

function getGenericErrorMessage(statusCode: number): string {
  switch (statusCode) {
    case StatusCodes.BAD_REQUEST:
      return 'Bad Request';
    case StatusCodes.UNAUTHORIZED:
      return 'Unauthorized';
    case StatusCodes.FORBIDDEN:
      return 'Forbidden';
    case StatusCodes.NOT_FOUND:
      return 'Not Found';
    case StatusCodes.CONFLICT:
      return 'Conflict';
    case StatusCodes.UNPROCESSABLE_ENTITY:
      return 'Validation Error';
    case StatusCodes.TOO_MANY_REQUESTS:
      return 'Too Many Requests';
    default:
      return 'Internal Server Error';
  }
}
