import { PrismaClient } from '@prisma/client';

import { logger } from './logger';

// Global variable to store Prisma client instance
declare global {
  // eslint-disable-next-line no-var, no-unused-vars
  var _prisma: PrismaClient | undefined;
}

// Create Prisma client instance
const createPrismaClient = (): PrismaClient =>
  new PrismaClient({
    log: [
      {
        emit: 'event',
        level: 'query',
      },
      {
        emit: 'event',
        level: 'error',
      },
      {
        emit: 'event',
        level: 'info',
      },
      {
        emit: 'event',
        level: 'warn',
      },
    ],
  });

// Use global variable in development to prevent multiple instances
const prisma = globalThis._prisma ?? createPrismaClient();

if (process.env.NODE_ENV === 'development') {
  globalThis._prisma = prisma;
}

// Set up logging for Prisma events
prisma.$on('query', e => {
  if (process.env.LOG_LEVEL === 'debug') {
    logger.debug('Query:', {
      query: e.query,
      params: e.params,
      duration: `${e.duration}ms`,
    });
  }
});

prisma.$on('error', e => {
  logger.error('Prisma error:', e);
});

prisma.$on('info', e => {
  logger.info('Prisma info:', e.message);
});

prisma.$on('warn', e => {
  logger.warn('Prisma warning:', e.message);
});

// Database connection function
export async function connectDatabase(): Promise<void> {
  try {
    await prisma.$connect();
    logger.info('Successfully connected to database');
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw error;
  }
}

// Database disconnection function
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('Successfully disconnected from database');
  } catch (error) {
    logger.error('Failed to disconnect from database:', error);
    throw error;
  }
}

export { prisma };
export default prisma;
