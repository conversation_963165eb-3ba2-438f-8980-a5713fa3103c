{"name": "iChat AI Assistant Development", "dockerComposeFile": ["../docker-compose.dev.yml", "docker-compose.yml"], "service": "devcontainer", "workspaceFolder": "/workspace", "shutdownAction": "stopCompose", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "18", "nodeGypDependencies": true, "installYarnUsingApt": false}, "ghcr.io/devcontainers/features/docker-in-docker:2": {"version": "latest", "enableNonRootDocker": "true"}, "ghcr.io/devcontainers/features/azure-cli:1": {"version": "latest"}, "ghcr.io/devcontainers/features/github-cli:1": {"version": "latest"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-json", "esbenp.prettier-vscode", "ms-azuretools.vscode-docker", "ms-vscode.azure-account", "ms-azuretools.vscode-azureappservice", "Prisma.prisma", "ms-vscode.vscode-eslint", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-jest", "ms-playwright.playwright", "ms-vscode.test-adapter-converter", "hbenl.vscode-test-explorer", "humao.rest-client", "eamodio.gitlens", "rangav.vscode-thunder-client", "usernamehw.errorlens", "steoates.autoimport", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-markdown"], "settings": {"typescript.preferences.includePackageJsonAutoImports": "on", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "eslint.workingDirectories": ["backend", "frontend", "cli", "shared"], "typescript.preferences.importModuleSpecifier": "relative", "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.DS_Store": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true}}}}, "forwardPorts": [3000, 5173, 5432, 6379], "portsAttributes": {"3000": {"label": "Backend API", "onAutoForward": "notify"}, "5173": {"label": "Frontend Dev Server", "onAutoForward": "openBrowser"}, "5432": {"label": "PostgreSQL", "onAutoForward": "silent"}, "6379": {"label": "Redis", "onAutoForward": "silent"}}, "postCreateCommand": "npm install && npm run setup:dev", "postStartCommand": "npm run dev", "remoteUser": "node", "mounts": ["source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached"]}