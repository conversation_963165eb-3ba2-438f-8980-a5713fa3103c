import chalk from 'chalk';
import { Command } from 'commander';

import { ConfigManager } from '../config';

export class ConfigCommand {
  private config: ConfigManager;

  constructor() {
    this.config = new ConfigManager();
  }

  public getCommand(): Command {
    const configCommand = new Command('config');
    configCommand.description('Configuration management commands');

    configCommand
      .command('get [key]')
      .description('Get configuration value(s)')
      .action(key => {
        if (key) {
          const value = this.config.get(key as any);
          console.log(`${key}: ${value}`);
        } else {
          const allConfig = this.config.getAll();
          Object.entries(allConfig).forEach(([k, v]) => {
            if (k === 'apiKey' && v) {
              console.log(`${k}: ${String(v).substring(0, 8)}...`);
            } else {
              console.log(`${k}: ${v}`);
            }
          });
        }
      });

    configCommand
      .command('set <key> <value>')
      .description('Set configuration value')
      .action((key, value) => {
        try {
          this.config.set(key as any, value);
          console.log(chalk.green(`✓ Set ${key} = ${value}`));
        } catch (error) {
          console.error(chalk.red(`Failed to set ${key}:`), error);
        }
      });

    return configCommand;
  }
}
