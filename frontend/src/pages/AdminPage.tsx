export function AdminPage() {
  return (
    <div className="max-w-6xl mx-auto py-8">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Administration</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">User Management</h3>
          </div>
          <div className="card-content">
            <p className="text-gray-600">
              Manage users, roles, and permissions
            </p>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Escalated Queries</h3>
          </div>
          <div className="card-content">
            <p className="text-gray-600">
              Review and respond to escalated queries
            </p>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">System Metrics</h3>
          </div>
          <div className="card-content">
            <p className="text-gray-600">
              Monitor system performance and usage
            </p>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold">Configuration</h3>
          </div>
          <div className="card-content">
            <p className="text-gray-600">
              Configure AI models and system settings
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
