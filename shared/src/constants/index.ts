// API Constants
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REFRESH: '/api/auth/refresh',
    LOGOUT: '/api/auth/logout',
  },
  CHAT: {
    SESSIONS: '/api/chat/sessions',
    MESSAGES: (sessionId: string) => `/api/chat/sessions/${sessionId}/messages`,
  },
  DOCUMENTS: {
    BASE: '/api/documents',
    UPLOAD: '/api/documents/upload',
    BY_ID: (id: string) => `/api/documents/${id}`,
  },
  USERS: {
    BASE: '/api/users',
    BY_ID: (id: string) => `/api/users/${id}`,
  },
  QUERIES: {
    ESCALATED: '/api/queries/escalated',
    RESPOND: (id: string) => `/api/queries/${id}/respond`,
    STATUS: (id: string) => `/api/queries/${id}/status`,
  },
  SYSTEM: {
    HEALTH: '/health',
    METRICS: '/api/metrics',
  },
} as const;

// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/markdown',
  ],
  ALLOWED_EXTENSIONS: ['.pdf', '.docx', '.txt', '.md'],
} as const;

// Pagination Constants
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100,
} as const;

// WebSocket Events
export const WEBSOCKET_EVENTS = {
  // Client to Server
  JOIN_SESSION: 'chat:join_session',
  SEND_MESSAGE: 'chat:send_message',
  TYPING: 'chat:typing',

  // Server to Client
  MESSAGE_RECEIVED: 'chat:message_received',
  TYPING_INDICATOR: 'chat:typing_indicator',
  ESCALATION_CREATED: 'chat:escalation_created',
  NOTIFICATION: 'system:notification',

  // Connection events
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ERROR: 'error',
} as const;

// LLM Constants
export const LLM_PROVIDERS = {
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic',
} as const;

export const LLM_MODELS = {
  OPENAI: {
    GPT_3_5_TURBO: 'gpt-3.5-turbo',
    GPT_4: 'gpt-4',
    GPT_4_TURBO: 'gpt-4-turbo-preview',
  },
  ANTHROPIC: {
    CLAUDE_3_HAIKU: 'claude-3-haiku-20240307',
    CLAUDE_3_SONNET: 'claude-3-sonnet-20240229',
    CLAUDE_3_OPUS: 'claude-3-opus-20240229',
  },
} as const;

// RAG Constants
export const RAG_CONFIG = {
  DEFAULT_CHUNK_SIZE: 1000,
  DEFAULT_CHUNK_OVERLAP: 200,
  DEFAULT_MAX_RETRIEVAL_RESULTS: 5,
  DEFAULT_CONFIDENCE_THRESHOLD: 0.7,
  EMBEDDING_DIMENSIONS: 1536, // OpenAI text-embedding-ada-002
} as const;

// Rate Limiting Constants
export const RATE_LIMITS = {
  DEFAULT_WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  DEFAULT_MAX_REQUESTS: 100,
  STRICT_WINDOW_MS: 5 * 60 * 1000, // 5 minutes
  STRICT_MAX_REQUESTS: 20,
} as const;

// Error Codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  LLM_ERROR: 'LLM_ERROR',
  DOCUMENT_PROCESSING_ERROR: 'DOCUMENT_PROCESSING_ERROR',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
} as const;
